"use client";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useGameStore } from "@/store/gameStore";
import { useTimeSettingsStore } from "@/store/timeSettingsStore";
import gameService from "@/services/gameService";

interface ThuneeCardDealingAnimationProps {
  isVisible: boolean;
  onComplete: () => void;
  dealingPhase: "dealFour" | "dealTwo";
  dealSpeed?: number; // Speed of dealing in milliseconds
}

export default function ThuneeCardDealingAnimation({
  isVisible,
  onComplete,
  dealingPhase,
  dealSpeed,
}: ThuneeCardDealingAnimationProps) {
  const { players, isDealer } = useGameStore();
  const { settings: timeSettings } = useTimeSettingsStore();

  // Use dealSpeed prop if provided, otherwise use settings
  const actualDealSpeed = dealSpeed ?? timeSettings.cardDealingSpeed;
  const [cardsDealt, setCardsDealt] = useState(0);
  const [currentPlayerIndex, setCurrentPlayerIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [message, setMessage] = useState("");
  const [dealingComplete, setDealingComplete] = useState(false);

  // Total cards to deal based on phase
  const totalCardsToDeal = dealingPhase === "dealFour" ? 16 : 8; // 4 or 2 cards per player

  // Reset state when component becomes visible
  useEffect(() => {
    if (isVisible) {
      console.log("ThuneeCardDealingAnimation: Component became visible, starting animation immediately");
      setCardsDealt(0);
      setCurrentPlayerIndex(0);
      setIsAnimating(false);
      setDealingComplete(false);
      setMessage(dealingPhase === "dealFour" ?
        "Dealing first 4 cards to each player..." :
        "Dealing final 2 cards to each player...");

      // Start the dealing animation immediately with a small delay to ensure state is set
      setTimeout(() => {
        setIsAnimating(true);
      }, 50);
    }
  }, [isVisible, dealingPhase]);

  // Listen for cards_dealt event to synchronize completion across all players
  useEffect(() => {
    const handleCardsDealt = () => {
      if (!isVisible) return;

      console.log("ThuneeCardDealingAnimation: Received cards_dealt event");
      setDealingComplete(true);
      setMessage(dealingPhase === "dealFour" ?
        "First 4 cards dealt to each player!" :
        "All cards dealt!");

      // Call onComplete after a delay
      setTimeout(() => {
        onComplete();
      }, 1500);
    };

    // Listen for the SignalR event
    const { gameService } = require('@/services/signalRService');
    gameService.on("cards_dealt", handleCardsDealt);

    return () => {
      gameService.off("cards_dealt", handleCardsDealt);
    };
  }, [isVisible, dealingPhase, onComplete]);

  // Handle the dealing animation
  useEffect(() => {
    if (!isVisible || !isAnimating || dealingComplete) return;

    // Function to deal a single card
    const dealCard = () => {
      // Calculate which player gets the card in counter-clockwise order
      // Following the Node.js implementation exactly:
      // Dealing order: player on right → partner → opponent's partner → dealer
      // This corresponds to positions: 1 (right) -> 3 (partner) -> 2 (opponent's partner) -> 0 (dealer)
      // But we need to map this to our visual layout: 0 (top) -> 1 (right) -> 2 (bottom) -> 3 (left)

      // For each round of 4 cards, we deal one card to each player in the correct order
      const dealOrder = [1, 3, 2, 0]; // Right, Partner, Opponent's Partner, Dealer (following Node.js pattern)
      const playerIndex = dealOrder[cardsDealt % 4];

      setCurrentPlayerIndex(playerIndex);

      // Increment cards dealt counter
      setCardsDealt(prev => prev + 1);

      // Check if we've dealt all cards locally (but don't complete until server confirms)
      if (cardsDealt + 1 >= totalCardsToDeal) {
        setMessage(dealingPhase === "dealFour" ?
          "Waiting for all players..." :
          "Waiting for all players...");
        console.log("ThuneeCardDealingAnimation: Local dealing complete, waiting for server confirmation");
        // Don't call onComplete here - wait for cards_dealt event from server
      }
    };

    // Set up interval to deal cards with a faster speed for better responsiveness
    const interval = setInterval(dealCard, Math.max(actualDealSpeed * 0.8, 200)); // 20% faster but not less than 200ms

    return () => clearInterval(interval);
  }, [isVisible, isAnimating, dealingComplete, cardsDealt, currentPlayerIndex, dealingPhase, actualDealSpeed, totalCardsToDeal, onComplete]);

  // If not visible, don't render anything
  if (!isVisible) {
    return null;
  }

  // Get player names
  const getPlayerName = (index: number) => {
    // If we have real players, use their names
    if (players.length === 4) {
      // Sort players by ID to ensure consistent order
      const sortedPlayers = [...players].sort((a, b) => a.id.localeCompare(b.id));
      return sortedPlayers[index]?.name || `Player ${index + 1}`;
    }

    // Default names
    return ["Top Player", "Right Player", "Bottom Player", "Left Player"][index];
  };

  // Calculate cards dealt to each player
  const getCardsDealtToPlayer = (playerIndex: number) => {
    // Define the dealing order (counter-clockwise)
    const dealOrder = [0, 1, 2, 3]; // Top, Right, Bottom, Left

    // Count how many cards have been dealt to this player
    let cardCount = 0;
    for (let i = 0; i < cardsDealt; i++) {
      if (dealOrder[i % 4] === playerIndex) {
        cardCount++;
      }
    }

    // Limit to max cards per player based on dealing phase
    const maxCards = dealingPhase === "dealFour" ? 4 : 2;
    return Math.min(cardCount, maxCards);
  };

  // Render dealer animation (4-direction dealing)
  if (isDealer) {
    return (
      <div className="fixed inset-0 bg-black/90 z-50 flex flex-col items-center justify-center">
        <h2 className="text-2xl font-bold text-[#E1C760] mb-4">
          {dealingPhase === "dealFour" ? "Dealing First 4 Cards" : "Dealing Final 2 Cards"}
        </h2>

        <div className="relative h-80 w-80 mb-8">
          {/* Deck in the center */}
          <div
            className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
            style={{
              backgroundImage: "url('/CardBack/card-back.svg')",
              backgroundSize: "cover",
              backgroundPosition: "center",
              left: "calc(50% - 40px)",
              top: "calc(50% - 56px)",
              zIndex: 1
            }}
          />

          {/* Dealing animation */}
          <AnimatePresence>
            {isAnimating && cardsDealt < totalCardsToDeal && (
              <motion.div
                key={`deal-card-${cardsDealt}`}
                className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
                style={{
                  backgroundImage: "url('/CardBack/card-back.svg')",
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                  left: "calc(50% - 40px)",
                  top: "calc(50% - 56px)",
                  zIndex: 10
                }}
                initial={{ x: 0, y: 0, rotate: 0, opacity: 1, scale: 1 }}
                animate={{
                  x: currentPlayerIndex === 0 ? 0 :
                     currentPlayerIndex === 1 ? 120 :
                     currentPlayerIndex === 2 ? 0 :
                     -120,
                  y: currentPlayerIndex === 0 ? -120 :
                     currentPlayerIndex === 1 ? 0 :
                     currentPlayerIndex === 2 ? 120 :
                     0,
                  rotate: [0, Math.random() * 10 - 5],
                  opacity: [1, 1, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 0.8, // Slower animation for better visibility
                  ease: "easeInOut"
                }}
              />
            )}
          </AnimatePresence>

          {/* Current player indicator */}
          <AnimatePresence>
            {isAnimating && cardsDealt < totalCardsToDeal && (
              <motion.div
                key={`highlight-${cardsDealt}`}
                className={`absolute rounded-full border-4 w-28 h-36`}
                style={{
                  borderColor: "#E1C760",
                  boxShadow: "0 0 15px rgba(225, 199, 96, 0.7)",
                  left: currentPlayerIndex === 0 ? "calc(50% - 56px)" :
                        currentPlayerIndex === 1 ? "calc(100% - 28px)" :
                        currentPlayerIndex === 2 ? "calc(50% - 56px)" :
                        "-28px",
                  top: currentPlayerIndex === 0 ? "-36px" :
                       currentPlayerIndex === 1 ? "calc(50% - 56px)" :
                       currentPlayerIndex === 2 ? "calc(100% - 36px)" :
                       "calc(50% - 56px)",
                  zIndex: 5
                }}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.3 }}
              />
            )}
          </AnimatePresence>

          {/* Player positions with card stacks */}
          {/* Top player */}
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 flex flex-col items-center">
            <div className="bg-blue-900 text-white px-3 py-1 rounded-full text-sm mb-2">
              {getPlayerName(0)}
            </div>
            <div className="relative w-20 h-28">
              {Array.from({ length: getCardsDealtToPlayer(0) }).map((_, index) => (
                <motion.div
                  key={`top-card-${index}`}
                  className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
                  style={{
                    backgroundImage: "url('/CardBack/card-back.svg')",
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    top: `${index * 2}px`,
                    left: `${index * 2}px`,
                    zIndex: index
                  }}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.2 }}
                />
              ))}
              {getCardsDealtToPlayer(0) === 0 && (
                <div className="w-full h-full border border-gray-600 rounded-md flex items-center justify-center bg-black/30">
                  <span className="text-gray-400 text-xs">Waiting...</span>
                </div>
              )}
            </div>
            <div className="mt-1 text-white text-xs">
              {getCardsDealtToPlayer(0)}/{dealingPhase === "dealFour" ? 4 : 2}
            </div>
          </div>

          {/* Right player */}
          <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 flex flex-row-reverse items-center">
            <div className="bg-red-900 text-white px-3 py-1 rounded-full text-sm ml-2">
              {getPlayerName(1)}
            </div>
            <div className="relative w-20 h-28">
              {Array.from({ length: getCardsDealtToPlayer(1) }).map((_, index) => (
                <motion.div
                  key={`right-card-${index}`}
                  className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
                  style={{
                    backgroundImage: "url('/CardBack/card-back.svg')",
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    top: `${index * 2}px`,
                    left: `${index * 2}px`,
                    zIndex: index
                  }}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.2 }}
                />
              ))}
              {getCardsDealtToPlayer(1) === 0 && (
                <div className="w-full h-full border border-gray-600 rounded-md flex items-center justify-center bg-black/30">
                  <span className="text-gray-400 text-xs">Waiting...</span>
                </div>
              )}
            </div>
            <div className="mr-1 text-white text-xs">
              {getCardsDealtToPlayer(1)}/{dealingPhase === "dealFour" ? 4 : 2}
            </div>
          </div>

          {/* Bottom player */}
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 flex flex-col-reverse items-center">
            <div className="bg-green-900 text-white px-3 py-1 rounded-full text-sm mt-2">
              {getPlayerName(2)}
            </div>
            <div className="relative w-20 h-28">
              {Array.from({ length: getCardsDealtToPlayer(2) }).map((_, index) => (
                <motion.div
                  key={`bottom-card-${index}`}
                  className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
                  style={{
                    backgroundImage: "url('/CardBack/card-back.svg')",
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    top: `${index * 2}px`,
                    left: `${index * 2}px`,
                    zIndex: index
                  }}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.2 }}
                />
              ))}
              {getCardsDealtToPlayer(2) === 0 && (
                <div className="w-full h-full border border-gray-600 rounded-md flex items-center justify-center bg-black/30">
                  <span className="text-gray-400 text-xs">Waiting...</span>
                </div>
              )}
            </div>
            <div className="mb-1 text-white text-xs">
              {getCardsDealtToPlayer(2)}/{dealingPhase === "dealFour" ? 4 : 2}
            </div>
          </div>

          {/* Left player */}
          <div className="absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2 flex flex-row items-center">
            <div className="bg-yellow-900 text-white px-3 py-1 rounded-full text-sm mr-2">
              {getPlayerName(3)}
            </div>
            <div className="relative w-20 h-28">
              {Array.from({ length: getCardsDealtToPlayer(3) }).map((_, index) => (
                <motion.div
                  key={`left-card-${index}`}
                  className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
                  style={{
                    backgroundImage: "url('/CardBack/card-back.svg')",
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    top: `${index * 2}px`,
                    left: `${index * 2}px`,
                    zIndex: index
                  }}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.2 }}
                />
              ))}
              {getCardsDealtToPlayer(3) === 0 && (
                <div className="w-full h-full border border-gray-600 rounded-md flex items-center justify-center bg-black/30">
                  <span className="text-gray-400 text-xs">Waiting...</span>
                </div>
              )}
            </div>
            <div className="ml-1 text-white text-xs">
              {getCardsDealtToPlayer(3)}/{dealingPhase === "dealFour" ? 4 : 2}
            </div>
          </div>
        </div>

        {/* Status message */}
        <div className="text-center text-white text-lg mt-4">
          {message}
        </div>

        {/* Progress bar */}
        <div className="w-64 h-2 bg-gray-800 rounded-full mt-4 overflow-hidden">
          <motion.div
            className="h-full bg-[#E1C760]"
            initial={{ width: "0%" }}
            animate={{ width: `${(cardsDealt / totalCardsToDeal) * 100}%` }}
            transition={{ duration: 0.3 }}
          />
        </div>
      </div>
    );
  }

  // Render player animation (direct to hand)
  return (
    <div className="fixed inset-0 bg-black/90 z-50 flex flex-col items-center justify-center">
      <h2 className="text-2xl font-bold text-[#E1C760] mb-4">
        {dealingPhase === "dealFour" ? "Receiving First 4 Cards" : "Receiving Final 2 Cards"}
      </h2>

      <div className="relative h-80 w-80 mb-8">
        {/* Dealer position */}
        <div className="absolute top-1/2 left-1/2 w-16 h-16 rounded-full bg-[#E1C760] transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center shadow-lg shadow-yellow-500/50 border-2 border-yellow-300">
          <span className="text-black font-bold text-sm">DEALER</span>
        </div>

        {/* Player position */}
        <div className="absolute bottom-0 left-1/2 w-20 h-20 rounded-full bg-blue-600 transform -translate-x-1/2 flex items-center justify-center shadow-lg shadow-blue-500/50 border-2 border-blue-300">
          <span className="text-white font-bold text-lg">YOU</span>
        </div>

        {/* Deck in center */}
        <div
          className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
          style={{
            backgroundImage: "url('/CardBack/card-back.svg')",
            backgroundSize: "cover",
            backgroundPosition: "center",
            left: "calc(50% - 40px)",
            top: "calc(50% - 56px)",
            zIndex: 1
          }}
        />

        {/* Cards being dealt to player */}
        <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 flex justify-center">
          <div className="relative w-[200px] h-[120px]">
            {Array.from({ length: Math.min(cardsDealt, dealingPhase === "dealFour" ? 4 : 2) }).map((_, index) => {
              // Calculate fan position
              const cardCount = dealingPhase === "dealFour" ? 4 : 2;
              const fanWidth = cardCount <= 2 ? 100 : 180;
              const offset = fanWidth / (cardCount - 1 || 1);
              const x = (-fanWidth / 2) + (offset * index);
              const rotation = -10 + (20 / (cardCount - 1 || 1)) * index;

              return (
                <motion.div
                  key={`player-card-${index}`}
                  className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
                  style={{
                    backgroundImage: "url('/CardBack/card-back.svg')",
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    zIndex: index
                  }}
                  initial={{
                    x: 0,
                    y: -100,
                    opacity: 0,
                    rotate: 0,
                    scale: 0.8
                  }}
                  animate={{
                    x: x,
                    y: 0,
                    opacity: 1,
                    rotate: rotation,
                    scale: 1
                  }}
                  transition={{
                    duration: 0.8,
                    ease: "easeOut",
                    delay: index * 0.1 // Stagger the animations
                  }}
                />
              );
            })}
          </div>
        </div>

        {/* Dealing animation */}
        <AnimatePresence>
          {isAnimating && cardsDealt < totalCardsToDeal && (
            <motion.div
              key={`deal-card-${cardsDealt}`}
              className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
              style={{
                backgroundImage: "url('/CardBack/card-back.svg')",
                backgroundSize: "cover",
                backgroundPosition: "center",
                left: "calc(50% - 40px)",
                top: "calc(50% - 56px)",
                zIndex: 10
              }}
              initial={{ x: 0, y: 0, rotate: 0, opacity: 1, scale: 1 }}
              animate={{
                x: 0,
                y: 120,
                rotate: [0, Math.random() * 10 - 5],
                opacity: [1, 1, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{
                duration: 0.8,
                ease: "easeInOut"
              }}
            />
          )}
        </AnimatePresence>

        {/* Card receiving highlight */}
        <AnimatePresence>
          {isAnimating && cardsDealt < totalCardsToDeal && (
            <motion.div
              key={`player-highlight-${cardsDealt}`}
              className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-28 h-28 rounded-full"
              style={{
                borderColor: "#3B82F6",
                boxShadow: "0 0 20px rgba(59, 130, 246, 0.7)",
                zIndex: 5
              }}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 0.5, scale: 1.2 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.4 }}
            />
          )}
        </AnimatePresence>
      </div>

      {/* Status message */}
      <div className="text-center text-white text-lg mt-4">
        {message}
      </div>

      {/* Progress bar */}
      <div className="w-64 h-2 bg-gray-800 rounded-full mt-4 overflow-hidden">
        <motion.div
          className="h-full bg-[#E1C760]"
          initial={{ width: "0%" }}
          animate={{ width: `${(cardsDealt / (dealingPhase === "dealFour" ? 4 : 2)) * 100}%` }}
          transition={{ duration: 0.3 }}
        />
      </div>
    </div>
  )
}