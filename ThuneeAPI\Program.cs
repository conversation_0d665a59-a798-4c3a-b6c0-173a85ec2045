using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using ThuneeAPI.Hubs;
using ThuneeAPI.Services;
using ThuneeAPI.Data;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();

// Add Entity Framework
builder.Services.AddDbContext<ThuneeDbContext>(options =>
{
    var connectionString = builder.Configuration.GetConnectionString("DefaultConnection")
        ?? "Server=(localdb)\\mssqllocaldb;Database=ThuneeDB;Trusted_Connection=true;MultipleActiveResultSets=true";
    options.UseSqlServer(connectionString);
});

// Add JWT Authentication
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var secretKey = jwtSettings["SecretKey"] ?? "YourSuperSecretKeyThatIsAtLeast32CharactersLong!";
var key = Encoding.ASCII.GetBytes(secretKey);

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = false,
        ValidateAudience = false,
        ClockSkew = TimeSpan.Zero
    };
});

builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "Thunee API", Version = "v1" });

    // Add JWT authentication to Swagger
    c.AddSecurityDefinition("Bearer", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new Microsoft.OpenApi.Models.OpenApiSecurityRequirement
    {
        {
            new Microsoft.OpenApi.Models.OpenApiSecurityScheme
            {
                Reference = new Microsoft.OpenApi.Models.OpenApiReference
                {
                    Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            new string[] {}
        }
    });
});

// Add SignalR
builder.Services.AddSignalR(options =>
{
    options.EnableDetailedErrors = true;
    options.KeepAliveInterval = TimeSpan.FromSeconds(15);
    options.ClientTimeoutInterval = TimeSpan.FromSeconds(60);
    options.HandshakeTimeout = TimeSpan.FromSeconds(30);
    options.MaximumReceiveMessageSize = 100 * 1024 * 1024; // 100MB
});

// Add CORS
var corsOrigins = builder.Configuration.GetSection("Cors:AllowedOrigins").Get<string[]>() ?? new[] { "*" };

builder.Services.AddCors(options =>
{
    if (builder.Environment.IsDevelopment())
    {
        options.AddPolicy("AllowAll", policy =>
        {
            policy.AllowAnyOrigin()
                  .AllowAnyMethod()
                  .AllowAnyHeader();
        });
    }
    else
    {
        options.AddPolicy("Production", policy =>
        {
            policy.WithOrigins(corsOrigins)
                  .AllowAnyMethod()
                  .AllowAnyHeader()
                  .AllowCredentials();
        });
    }
});

// Register existing services
builder.Services.AddSingleton<ILobbyService, LobbyService>();
builder.Services.AddSingleton<IGameService, GameService>();
builder.Services.AddSingleton<ISpectatorService, SpectatorService>();
builder.Services.AddSingleton<IVideoService, VideoService>();
builder.Services.AddSingleton<ICardService, CardService>();
builder.Services.AddSingleton<IBallService, BallService>();
builder.Services.AddSingleton<ITurnService, TurnService>();
builder.Services.AddSingleton<ITimeframeService, TimeframeService>();

// Register new services
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<ICompetitionService, CompetitionService>();

var app = builder.Build();

// Ensure database is created
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<ThuneeDbContext>();
    context.Database.EnsureCreated();
}

// Wire up service events to SignalR
var lobbyService = app.Services.GetRequiredService<ILobbyService>();
var gameService = app.Services.GetRequiredService<IGameService>();
var turnService = app.Services.GetRequiredService<ITurnService>();

// Get the hub context for broadcasting events
var gameHubContext = app.Services.GetRequiredService<IHubContext<ThuneeAPI.Hubs.GameHub>>();

// Wire up lobby service events
lobbyService.PlayersUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("players_updated", data);
};

lobbyService.TeamNamesUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("team_names_updated", data);
};

lobbyService.TeamReadyUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("team_ready_updated", data);
};

lobbyService.GameStarted += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("game_started", data);
};

lobbyService.GamePhaseUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("game_phase_updated", data);
};

lobbyService.MatchFound += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("match_found", data);
};

lobbyService.MatchStatusUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("match_status_update", data);
};

lobbyService.TimeframeOptionsUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("timeframe_options", data);
};

// Wire up game service events
gameService.TimeframeOptionsUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("timeframe_options", data);
};

gameService.TimeframeVoteUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("timeframe_vote_updated", data);
};

gameService.CardDealt += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("card_dealt", data);
};

gameService.DealerFound += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("dealer_found", data);
};

gameService.CardsDealt += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("cards_dealt", data);
};

gameService.TrumpSelected += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("trump_selected", data);
};

gameService.PlayerTurn += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("player_turn", data);
};

gameService.CardPlayed += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("card_played", data);
};

gameService.HandCompleted += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("hand_completed", data);
};

gameService.BallCompleted += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("ball_completed", data);
};

gameService.GameEnded += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("game_ended", data);
};

gameService.JordhiCalled += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("jordhi_called", data);
};

gameService.JordhiCardsRevealed += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("jordhi_cards_revealed", data);
};

gameService.FourBallResult += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("four_ball_result", data);
};

gameService.GamePhaseUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("game_phase_updated", data);
};

gameService.ShuffleAnimation += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("shuffle_animation", data);
};

gameService.ShuffleComplete += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("shuffle_complete", data);
};

gameService.CutRequested += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("cut_requested", data);
};

gameService.FirstFourDealt += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("first_four_dealt", data);
};

gameService.ShuffleAnimation += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("shuffle_animation", data);
};

gameService.ShuffleComplete += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("shuffle_complete", data);
};

gameService.CutRequested += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("cut_requested", data);
};

gameService.CutComplete += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("cut_complete", data);
};

gameService.BiddingCompleted += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("bidding_complete", data);
};

// Wire up turn service events
turnService.PlayerTurnUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("player_turn_updated", data);
};

turnService.TurnTimerUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("turn_timer_updated", data);
};

turnService.TurnTimeout += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("turn_timeout", data);
};

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
    app.UseCors("AllowAll");
}
else
{
    app.UseCors("Production");
}

app.UseHttpsRedirection();
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Map SignalR hubs
app.MapHub<GameHub>("/gameHub");
app.MapHub<VideoHub>("/videoHub");

// Add a simple test endpoint
app.MapGet("/", () =>
{
    var port = app.Environment.IsDevelopment() ? "3001" : "96";
    return Results.Content($@"
    <html>
      <head>
        <title>Thunee SignalR Server</title>
        <style>
          body {{ font-family: Arial, sans-serif; background: #222; color: #E1C760; margin: 0; padding: 20px; text-align: center; }}
          .container {{ max-width: 800px; margin: 0 auto; background: #333; padding: 20px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.3); }}
          h1 {{ color: #E1C760; }}
          .status {{ padding: 10px; background: #444; border-radius: 4px; margin: 20px 0; }}
          .success {{ color: #4CAF50; }}
        </style>
      </head>
      <body>
        <div class='container'>
          <h1>Thunee SignalR Server</h1>
          <div class='status'>
            <p>Server is running on port <strong>{port}</strong></p>
            <p class='success'>✓ SignalR server is active</p>
          </div>
          <p>This server handles SignalR connections for the Thunee card game.</p>
          <p>To play the game, please open the Thunee game client application.</p>
          <p><a href='/debug/match-queue' style='color: #E1C760;'>View Match Queue Debug Info</a></p>
        </div>
      </body>
    </html>", "text/html");
});

app.Run();
