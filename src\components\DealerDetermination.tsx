import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Player } from "@/store/lobbyStore";
import { useGameStore } from "@/store/gameStore";
import gameService from "@/services/gameService";
import { getCardImagePath } from "@/utils/cardUtils";

// Define the card type
type Card = {
  suit: "hearts" | "diamonds" | "clubs" | "spades";
  value: string;
};

interface PlayerCardState {
  playerId: string;
  card: Card | null;
  isDealer: boolean;
}

interface DealerDeterminationProps {
  players: Player[];
  teamNames?: { 1: string; 2: string };
  onDealerSelected: () => void;
}

export default function DealerDetermination({
  players,
  teamNames,
  onDealerSelected,
}: DealerDeterminationProps) {
  const [currentPlayerIndex, setCurrentPlayerIndex] = useState(0);
  const [dealerFound, setDealerFound] = useState(false);
  const [dealerId, setDealerId] = useState<string | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const [message, setMessage] = useState("Determining the dealer...");
  const [playerCards, setPlayerCards] = useState<PlayerCardState[]>([]);
  const [currentCard, setCurrentCard] = useState<Card | null>(null);
  const [allPlayers, setAllPlayers] = useState<Player[]>(players);
  const [dealerDeterminationStarted, setDealerDeterminationStarted] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const isTransitioningRef = useRef(false);

  // Initialize player cards state
  useEffect(() => {
    if (players.length > 0) {
      console.log(
        "Initial players in DealerDetermination:",
        players.map((p) => `${p.name} (${p.id}) - Team ${p.team}`)
      );

      // Sort players by ID to ensure consistent order across all clients
      const sortedPlayers = [...players].sort((a, b) =>
        a.id.localeCompare(b.id)
      );
      setAllPlayers(sortedPlayers);

      // Only initialize player cards if not already done
      if (playerCards.length === 0) {
        setPlayerCards(
          sortedPlayers.map((player) => ({
            playerId: player.id,
            card: null,
            isDealer: false,
          }))
        );

        // Only the first player (index 0) should start the dealer determination process
        // to avoid multiple simultaneous starts
        const currentPlayerId = gameService.getSocketId();
        const firstPlayer = sortedPlayers[0];

        if (firstPlayer && firstPlayer.id === currentPlayerId && !dealerDeterminationStarted) {
          console.log("I am the first player, starting dealer determination");
          setDealerDeterminationStarted(true); // Prevent multiple calls

          // Add a shorter delay to ensure all clients have loaded the component
          setTimeout(() => {
            // Start the dealer determination process on the server
            // This will trigger the dealer_determination_started event
            // which will ensure all clients have the same players
            gameService
              .sendGameAction("start_dealer_determination", {})
              .catch((error) => {
                console.error("Error starting dealer determination:", error);
                // If we get an error about dealer determination already in progress or dealer already found,
                // just wait for the events from the server
                if (
                  error.message &&
                  (error.message.includes("already in progress") || error.message.includes("already found"))
                ) {
                  console.log(
                    "Dealer determination already in progress or dealer already found, waiting for server events"
                  );
                } else {
                  // If there was a different error, reset the flag and try again after a delay
                  setDealerDeterminationStarted(false);
                  setTimeout(() => {
                    console.log("Retrying dealer determination start...");
                    // Check if dealer is already found before retrying
                    if (!dealerFound && !isTransitioning) {
                      setDealerDeterminationStarted(true);
                      gameService
                        .sendGameAction("start_dealer_determination", {})
                        .catch((retryError) => {
                          console.error("Error on retry:", retryError);
                          setDealerDeterminationStarted(false);
                        });
                    } else {
                      console.log("Dealer already found or transitioning, skipping retry");
                    }
                  }, 2000); // 2 second delay before retry
                }
              });
          }, 1000); // 1 second delay
        } else {
          console.log("Waiting for first player to start dealer determination");
        }
      }
    }
  }, [players.length, dealerDeterminationStarted, dealerFound, isTransitioning]);

  // Listen for reset dealer determination event
  useEffect(() => {
    const handleResetDealerDetermination = () => {
      console.log(`Reset event received. isTransitioning: ${isTransitioning}, isTransitioningRef: ${isTransitioningRef.current}, dealerFound: ${dealerFound}, dealerId: ${dealerId}`);

      // If we're already transitioning to the next phase, ignore reset events
      if (isTransitioning || isTransitioningRef.current) {
        console.log("Ignoring reset_dealer_determination event - already transitioning to next phase");
        return;
      }

      console.log("Resetting dealer determination state");
      setAllPlayers([]);
      setPlayerCards([]);
      setCurrentPlayerIndex(0);
      setCurrentCard(null);
      setDealerId(null);
      setDealerFound(false);
      setMessage("Determining the dealer...");
      setIsAnimating(false);
      setDealerDeterminationStarted(false); // Reset the flag

      // Also reset the game store dealer state
      const { updateGameState } = useGameStore.getState();
      updateGameState({
        isDealer: false,
        isTrumpSelector: false,
      });
    };

    gameService.on(
      "reset_dealer_determination",
      handleResetDealerDetermination
    );

    return () => {
      gameService.off(
        "reset_dealer_determination",
        handleResetDealerDetermination
      );
    };
  }, [isTransitioning]);

  // Listen for dealer determination started event
  useEffect(() => {
    const handleDealerDeterminationStarted = (data: {
      players: Player[];
      currentPlayerIndex: number;
    }) => {
      console.log("Dealer determination started:", data);

      // Ensure we have all 4 players
      if (data.players.length !== 4) {
        console.error("Expected 4 players but got", data.players.length);
        return; // Don't proceed if we don't have exactly 4 players
      }

      // Sort players by ID to ensure consistent order across all clients
      const sortedPlayers = [...data.players].sort((a, b) =>
        a.id.localeCompare(b.id)
      );

      // Reset all state to ensure consistency
      setDealerFound(false);
      setDealerId(null);
      setCurrentCard(null);
      setIsAnimating(false);
      setMessage("Determining the dealer...");

      // Clear players and cards with a small delay to ensure clean state
      setAllPlayers([]);
      setPlayerCards([]);

      // After a small delay to ensure state is reset
      setTimeout(() => {
        // Set the players in the sorted order
        setAllPlayers(sortedPlayers);

        // Find the index of the current player in the sorted array
        const currentPlayer = data.players[data.currentPlayerIndex];
        const sortedIndex = sortedPlayers.findIndex(
          (p) => p.id === currentPlayer.id
        );
        setCurrentPlayerIndex(sortedIndex >= 0 ? sortedIndex : 0);

        // Initialize player cards state with all players
        // Make sure we have all players from both teams
        setPlayerCards(
          sortedPlayers.map((player) => ({
            playerId: player.id,
            card: null,
            isDealer: false,
          }))
        );

        // Log all players for debugging
        console.log(
          "All players in dealer determination:",
          sortedPlayers.map((p) => `${p.name} (${p.id}) - Team ${p.team}`)
        );
      }, 500); // Increased delay for more reliability
    };

    gameService.on(
      "dealer_determination_started",
      handleDealerDeterminationStarted
    );

    return () => {
      gameService.off(
        "dealer_determination_started",
        handleDealerDeterminationStarted
      );
    };
  }, []);

  // Listen for dealing card to player event
  useEffect(() => {
    const handleDealingCardTo = (data: {
      playerId: string;
      playerName: string;
      playerTeam: number;
      playerIndex: number;
    }) => {
      // console.log("Dealing card to player:", data);

      // Find the player who will receive the card
      const player = allPlayers.find((p) => p.id === data.playerId);
      if (!player) {
        console.error("Player not found in allPlayers:", data.playerId);
        setMessage(`Dealing to: ${data.playerName} (Team ${data.playerTeam})`);
        return;
      }

      // Find the correct index in our sorted array instead of using server index
      const playerIndex = allPlayers.findIndex((p) => p.id === data.playerId);
      setCurrentPlayerIndex(playerIndex >= 0 ? playerIndex : 0);

      // Show more detailed message about which player is receiving the card
      setMessage(`Dealing to: ${player.name} (Team ${player.team})`);
    };

    gameService.on("dealing_card_to", handleDealingCardTo);

    return () => {
      gameService.off("dealing_card_to", handleDealingCardTo);
    };
  }, [allPlayers]);

  // Listen for card dealt events
  useEffect(() => {
    const handleCardDealt = (data: {
      playerId: string;
      playerName: string;
      playerTeam: number;
      playerIndex: number;
      card: Card;
      isDealer: boolean;
      cardsDealtToPlayer: number;
      totalCardsDealt: number;
    }) => {
      console.log("Card dealt:", data);
      setIsAnimating(true);

      // Find the player who received the card
      const player = allPlayers.find((p) => p.id === data.playerId);

      // Find the correct index in our sorted array instead of using server index
      const playerIndex = allPlayers.findIndex((p) => p.id === data.playerId);
      setCurrentPlayerIndex(playerIndex >= 0 ? playerIndex : 0);

      // Set the current card being dealt - this is the card that will be shown in the center
      // and should be the same for all players
      setCurrentCard(data.card);

      // Update message to show which player is receiving the card with more details
      const playerName = player ? player.name : data.playerName;
      const playerTeam = player ? player.team : data.playerTeam;

      // Show more detailed message including the card being dealt
      setMessage(`Dealing ${data.card.value} of ${data.card.suit} to: ${playerName} (Team ${playerTeam})`);

      // After a delay, update the player's card in their display area
      setTimeout(() => {
        // Update the player cards state to show the card in the player's area
        setPlayerCards((prevCards) => {
          return prevCards.map((pc) => {
            if (pc.playerId === data.playerId) {
              return { ...pc, card: data.card };
            }
            return pc;
          });
        });

        // If this is the dealer (black jack), update state
        if (data.isDealer) {
          setDealerId(data.playerId);
          setDealerFound(true);
          setMessage(`${playerName} got a Black Jack (${data.card.value} of ${data.card.suit}) and will be the dealer!`);

          // Mark this player as dealer
          setPlayerCards((prevCards) => {
            return prevCards.map((pc) => {
              if (pc.playerId === data.playerId) {
                return { ...pc, isDealer: true };
              }
              return pc;
            });
          });
        }

        setIsAnimating(false);
      }, 1000);
    };

    gameService.on("card_dealt", handleCardDealt);

    return () => {
      gameService.off("card_dealt", handleCardDealt);
    };
  }, [allPlayers]);

  // Listen for dealer found event
  useEffect(() => {
    const handleDealerFound = (data: {
      dealerId: string;
      dealerName: string;
      dealerTeam: number;
      trumpSelectorId: string;
      trumpSelectorName: string;
      trumpSelectorTeam: number;
      dealerCard: Card;
      players: Player[];
    }) => {
      console.log("Dealer found:", data);

      // Set transitioning flag IMMEDIATELY to prevent any reset events from interfering
      console.log("Setting isTransitioning to true to prevent resets");
      isTransitioningRef.current = true;
      setIsTransitioning(true);

      setDealerId(data.dealerId);
      setDealerFound(true);

      // Use the dealer information directly from the server data
      // This ensures consistency across all clients
      setMessage(`${data.dealerName} got a Black Jack (${data.dealerCard.value} of ${data.dealerCard.suit}) and will be the dealer!`);

      // Update the game store for ALL players to ensure consistency
      const currentPlayerId = gameService.getSocketId();
      const { updateGameState, players } = useGameStore.getState();

      // Update players array with dealer and trump selector information
      const updatedPlayers = players.map((player) => ({
        ...player,
        isDealer: player.id === data.dealerId,
        isTrumpSelector: player.id === data.trumpSelectorId,
      }));

      // Update game state for current player
      updateGameState({
        players: updatedPlayers,
        isDealer: data.dealerId === currentPlayerId,
        isTrumpSelector: data.trumpSelectorId === currentPlayerId,
      });

      console.log(`Dealer found: ${data.dealerName} (${data.dealerId}), Current player: ${currentPlayerId}`);
      console.log(`Is current player the dealer? ${data.dealerId === currentPlayerId}`);

      // Mark this player as dealer in the UI
      setPlayerCards((prevCards) => {
        return prevCards.map((pc) => {
          return {
            ...pc,
            isDealer: pc.playerId === data.dealerId,
          };
        });
      });

      // Add a visual indicator for the trump selector as well
      setTimeout(() => {
        // After showing the dealer, also highlight the trump selector
        setMessage(`${data.dealerName} is the dealer. ${data.trumpSelectorName} (Team ${data.trumpSelectorTeam}) will select trump.`);
      }, 3000);
    };

    gameService.on("dealer_found", handleDealerFound);

    return () => {
      gameService.off("dealer_found", handleDealerFound);
    };
  }, [allPlayers]);

  // Listen for dealer determination aborted event
  useEffect(() => {
    const handleDealerDeterminationAborted = (data: { reason: string }) => {
      console.log("Dealer determination aborted:", data.reason);
      setMessage(`Dealer determination aborted: ${data.reason}. Restarting...`);

      // Reset state
      setDealerFound(false);
      setDealerId(null);
      setCurrentCard(null);
      setIsAnimating(false);
      setPlayerCards([]);

      // After a delay, try to restart the dealer determination
      setTimeout(() => {
        // Only the first player should restart
        const currentPlayerId = gameService.getSocketId();
        const firstPlayer = allPlayers[0];

        if (firstPlayer && firstPlayer.id === currentPlayerId && !dealerDeterminationStarted) {
          console.log("I am the first player, restarting dealer determination");
          setDealerDeterminationStarted(true);
          gameService
            .sendGameAction("start_dealer_determination", {})
            .catch((error) => {
              console.error("Error restarting dealer determination:", error);
              setDealerDeterminationStarted(false);
            });
        }
      }, 3000); // 3 second delay before restarting
    };

    gameService.on(
      "dealer_determination_aborted",
      handleDealerDeterminationAborted
    );

    return () => {
      gameService.off(
        "dealer_determination_aborted",
        handleDealerDeterminationAborted
      );
    };
  }, [allPlayers]);

  // When dealer is found, notify parent after a delay
  useEffect(() => {
    if (dealerFound && dealerId && isTransitioning) {
      // Log dealer information
      const currentPlayerId = gameService.getSocketId();
      console.log(
        `Dealer found: ${dealerId}, Current player: ${currentPlayerId}`
      );
      console.log(
        `Is current player the dealer? ${dealerId === currentPlayerId}`
      );

      // Add a delay to show the dealer before continuing
      const timer = setTimeout(() => {
        console.log("Transitioning to next phase after dealer determination");
        onDealerSelected();
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [dealerFound, dealerId, onDealerSelected, isTransitioning]);

  return (
    <div className="fixed inset-0 bg-black/90 z-50 flex flex-col items-center justify-center p-4">
      <h2 className="text-xl font-bold text-[#E1C760] mb-2">
        Determining the Dealer...
      </h2>
      <p className="text-white text-center mb-3 max-w-xs text-xs">
        The first player to receive a black Jack (Jack of Clubs or Jack of
        Spades) will be the dealer.
      </p>

      <div className="mb-2 text-center">
        <p className="text-white text-sm font-semibold mb-1">
          Dealing to:{" "}
          <span className="text-[#E1C760]">
            {allPlayers[currentPlayerIndex]?.name || ""}
          </span>
        </p>
        <p className="text-[#E1C760] text-xs">
          Everyone sees this card being dealt
        </p>
      </div>

      {/* Current card being dealt - this is shown to all players */}
      <div className="relative h-32 w-24 mb-3">
        <AnimatePresence>
          {currentCard && isAnimating && (
            <motion.div
              key={`current-${currentCard.suit}-${
                currentCard.value
              }-${Date.now()}`}
              initial={{ y: -50, opacity: 0, rotateY: 180, scale: 0.8 }}
              animate={{ y: 0, opacity: 1, rotateY: 0, scale: 1 }}
              exit={{ y: 50, opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.4, ease: "easeOut" }}
              className="absolute inset-0"
            >
              <div className="relative">
                <div className="border-2 border-[#E1C760] rounded-md overflow-hidden shadow-lg shadow-[#E1C760]/20">
                  <img
                    src={getCardImagePath(currentCard.value, currentCard.suit)}
                    alt={`${currentCard.value} of ${currentCard.suit}`}
                    className="w-full h-full object-contain"
                  />
                </div>

                {/* Player name label */}
                <div className="absolute -bottom-6 left-0 right-0 text-center">
                  <span className="bg-[#E1C760] text-black text-xs px-2 py-0.5 rounded-full font-medium">
                    {allPlayers[currentPlayerIndex]?.name || ""}
                  </span>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Player grid with their cards - ensure consistent layout for 4 players */}
      <div className="grid grid-cols-2 gap-x-4 gap-y-4 mb-4 w-full max-w-xs">
        {/* Debug info - hidden in production */}
        <div className="hidden col-span-2 text-xs text-gray-400 mb-2">
          <p>
            Players: {allPlayers.length} | Cards: {playerCards.length} | Current
            Player: {currentPlayerIndex}
          </p>
        </div>

        {/* Show a waiting message until we have all players and cards */}
        {allPlayers.length === 4 && playerCards.length === 4 ? (
          allPlayers.map((player, index) => {
            // Find or create a card state for this player
            let playerCardState = playerCards.find(
              (pc) => pc.playerId === player.id
            );

            // If player card state doesn't exist, create it
            if (!playerCardState) {
              console.warn(
                "Creating missing card state for player:",
                player.id,
                player.name
              );

              // Create a new card state for this player
              const newCardState = {
                playerId: player.id,
                card: null,
                isDealer: false,
              };

              // Add it to the playerCards array - but don't call setState during render
              // Instead, use a timeout to update after render
              setTimeout(() => {
                setPlayerCards((prev) => [...prev, newCardState]);
              }, 0);

              // Use the new card state for this render
              playerCardState = newCardState;
            }

            return (
              <div
                key={player.id}
                className={`flex flex-col items-center p-2 rounded-lg ${
                  currentPlayerIndex === index
                    ? "bg-[#E1C760]/10 ring-1 ring-[#E1C760]"
                    : ""
                }`}
              >
                <div className="flex flex-col items-center mb-2">
                  <div
                    className={`w-10 h-10 rounded-full overflow-hidden border-2 ${
                      dealerId === player.id
                        ? "border-green-500"
                        : currentPlayerIndex === index
                        ? "border-[#E1C760]"
                        : "border-gray-600"
                    }`}
                  >
                    <img
                      src={player.avatar || `https://api.dicebear.com/7.x/avataaars/svg?seed=${player.name}`}
                      alt={player.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="mt-1 text-center">
                    <span
                      className={`block text-sm font-medium ${
                        currentPlayerIndex === index
                          ? "text-[#E1C760]"
                          : "text-white"
                      }`}
                    >
                      {player.name}
                    </span>
                    {dealerId === player.id && (
                      <span className="text-green-500 text-xs font-bold block">
                        DEALER
                      </span>
                    )}
                  </div>
                </div>

                {/* Player's card */}
                <div className="h-24 w-16 relative">
                  <AnimatePresence>
                    {playerCardState?.card ? (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3 }}
                        className="border-2 border-[#E1C760] rounded-md overflow-hidden shadow-md"
                      >
                        <img
                          src={getCardImagePath(
                            playerCardState.card.value,
                            playerCardState.card.suit
                          )}
                          alt={`${playerCardState.card.value} of ${playerCardState.card.suit}`}
                          className="w-full h-full object-contain"
                        />

                        {/* Highlight if it's a black jack */}
                        {playerCardState.isDealer && (
                          <div className="absolute inset-0 border-4 border-green-500 rounded-md animate-pulse"></div>
                        )}
                      </motion.div>
                    ) : (
                      <div className="w-full h-full border border-gray-600 rounded-md flex items-center justify-center bg-black/30">
                        <span className="text-gray-400 text-xs">
                          Waiting...
                        </span>
                      </div>
                    )}
                  </AnimatePresence>
                </div>

                {/* Show team info */}
                <div className="mt-2 text-xs text-center">
                  <span
                    className={`px-2 py-1 rounded-full font-medium ${
                      player.team === 1
                        ? "bg-blue-900 text-blue-200"
                        : "bg-red-900 text-red-200"
                    }`}
                  >
                    Team {player.team}
                  </span>
                </div>
              </div>
            );
          })
        ) : (
          // If we don't have exactly 4 players or 4 card states, show a loading message
          <div className="col-span-2 text-center text-white">
            <p className="text-xs">Waiting for all players to join...</p>
            <p className="text-xs text-gray-400 mt-1">
              Waiting for {4 - Math.min(allPlayers.length, 4)} more players...
            </p>
          </div>
        )}
      </div>

      {/* Black Jack examples */}
      {!dealerFound && (
        <div className="flex flex-col items-center mb-3">
          <p className="text-white mb-1 text-xs">
            Looking for one of these cards:
          </p>
          <div className="flex space-x-3">
            <div className="flex flex-col items-center">
              <div className="border border-[#E1C760] rounded-md overflow-hidden h-16 w-12">
                <img
                  src={getCardImagePath("J", "clubs")}
                  alt="Jack of Clubs"
                  className="h-full w-full object-contain"
                />
              </div>
            </div>
            <div className="flex flex-col items-center">
              <div className="border border-[#E1C760] rounded-md overflow-hidden h-16 w-12">
                <img
                  src={getCardImagePath("J", "spades")}
                  alt="Jack of Spades"
                  className="h-full w-full object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {dealerFound && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center bg-black/50 p-4 rounded-lg border border-[#E1C760]/30 max-w-xs"
        >
          <div className="mb-4">
            {allPlayers.map((player) => {
              if (player.id === dealerId) {
                return (
                  <div key={player.id} className="flex flex-col items-center">
                    <div className="relative w-16 h-16 mb-3">
                      <div className="absolute inset-0 bg-green-500 rounded-full animate-ping opacity-30"></div>
                      <div className="absolute inset-0 bg-[#E1C760]/20 rounded-full animate-pulse"></div>
                      <div className="relative w-16 h-16 rounded-full overflow-hidden border-3 border-green-500">
                        <img
                          src={player.avatar || `https://api.dicebear.com/7.x/avataaars/svg?seed=${player.name}`}
                          alt={player.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                    <motion.p
                      initial={{ y: 10, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.3 }}
                      className="text-white text-sm font-medium"
                    >
                      <span className="text-[#E1C760] font-bold text-base">
                        {player.name}
                      </span>{" "}
                      is the dealer!
                    </motion.p>
                  </div>
                );
              }
              return null;
            })}
          </div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
          >
            <p className="text-white mb-3 text-sm">
              The dealer will now deal the cards for the game.
            </p>
            <Button
              variant="default"
              className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80 font-medium py-2 px-4"
              onClick={() => {
                // Just log and continue
                const currentPlayerId = gameService.getSocketId();
                console.log(
                  `Continue to Game clicked. Current player: ${currentPlayerId}, Dealer: ${dealerId}`
                );
                isTransitioningRef.current = true;
                setIsTransitioning(true);
                onDealerSelected();
              }}
            >
              Continue to Game
            </Button>
          </motion.div>
        </motion.div>
      )}
    </div>
  );
}
