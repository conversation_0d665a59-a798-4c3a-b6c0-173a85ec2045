{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Hubs\\GameHub.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:<PERSON><PERSON>\\GameHub.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Hubs\\VideoHub.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Hubs\\VideoHub.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Data\\ThuneeDbContext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Data\\ThuneeDbContext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Controllers\\PlayerController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Controllers\\PlayerController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Controllers\\CompetitionController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Controllers\\CompetitionController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Controllers\\DebugController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Controllers\\DebugController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\deploy-to-iis.ps1||{3B902123-F8A7-4915-9F01-361F908088D0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:deploy-to-iis.ps1||{3B902123-F8A7-4915-9F01-361F908088D0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Services\\AuthService.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Services\\AuthService.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\client-integration\\README.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:client-integration\\README.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\ThuneeAPI.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:ThuneeAPI.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "VideoHub.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Hubs\\VideoHub.cs", "RelativeDocumentMoniker": "Hubs\\VideoHub.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Hubs\\VideoHub.cs", "RelativeToolTip": "Hubs\\VideoHub.cs", "ViewState": "AgIAADwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T06:48:08.218Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "GameHub.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Hubs\\GameHub.cs", "RelativeDocumentMoniker": "Hubs\\GameHub.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Hubs\\GameHub.cs", "RelativeToolTip": "Hubs\\GameHub.cs", "ViewState": "AgIAAFQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T06:48:07.135Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "ThuneeDbContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Data\\ThuneeDbContext.cs", "RelativeDocumentMoniker": "Data\\ThuneeDbContext.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Data\\ThuneeDbContext.cs", "RelativeToolTip": "Data\\ThuneeDbContext.cs", "ViewState": "AgIAADYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T06:47:56.22Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "PlayerController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Controllers\\PlayerController.cs", "RelativeDocumentMoniker": "Controllers\\PlayerController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Controllers\\PlayerController.cs", "RelativeToolTip": "Controllers\\PlayerController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T06:47:54.471Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "CompetitionController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Controllers\\CompetitionController.cs", "RelativeDocumentMoniker": "Controllers\\CompetitionController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Controllers\\CompetitionController.cs", "RelativeToolTip": "Controllers\\CompetitionController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T06:47:53.238Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "DebugController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Controllers\\DebugController.cs", "RelativeDocumentMoniker": "Controllers\\DebugController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Controllers\\DebugController.cs", "RelativeToolTip": "Controllers\\DebugController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T06:47:51.126Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "deploy-to-iis.ps1", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\deploy-to-iis.ps1", "RelativeDocumentMoniker": "deploy-to-iis.ps1", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\deploy-to-iis.ps1", "RelativeToolTip": "deploy-to-iis.ps1", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-06-02T06:47:48.211Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\appsettings.json", "RelativeDocumentMoniker": "appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\appsettings.json", "RelativeToolTip": "appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-02T06:47:46.513Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "AuthService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Services\\AuthService.cs", "RelativeDocumentMoniker": "Services\\AuthService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\Services\\AuthService.cs", "RelativeToolTip": "Services\\AuthService.cs", "ViewState": "AgIAAHUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T06:47:29.269Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "README.md", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\client-integration\\README.md", "RelativeDocumentMoniker": "client-integration\\README.md", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\client-integration\\README.md", "RelativeToolTip": "client-integration\\README.md", "ViewState": "AgIAAIEAAAAAAAAAAAAAAAsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-05-29T07:12:37.256Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "ThuneeAPI.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\ThuneeAPI.csproj", "RelativeDocumentMoniker": "ThuneeAPI.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\ThuneeAPI.csproj", "RelativeToolTip": "ThuneeAPI.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-05-29T07:12:15.925Z"}]}]}]}